import React, { useCallback, useRef, useState } from "react";
import { RichTextState } from "@src/lib/richText";
import { ScrollView, View } from "react-native";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaTableOfContents } from "@src/components/_atoms/SeaTableOfContents/SeaTableOfContents";
import SeaRichTextViewer from "@src/components/_dom/lexical/SeaRichText/SeaRichTextViewer";
import { useDeviceWidth } from "@src/hooks/useDevice";
import { showToast } from "@src/managers/ToastManager/ToastManager";

export interface SeaRichTextProps {
  forModal: boolean;
  visible: boolean;
  setOnScroll: React.Dispatch<
    React.SetStateAction<((event: any) => void) | undefined>
  >;
  richTextState: RichTextState;
  modalContentRef?: React.RefObject<ScrollView>;
  editButtons?: JSX.Element;
  onlineStatus?: boolean;
  isEditable?: boolean;
  onSaveChanges?: (json: any) => void;
}

const SeaRichText = ({ richTextState, ...rest }: SeaRichTextProps) => {
  const [sections, setSections] = useState<any[]>();
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollOnChange = useRef(false); // If currently true, a change in activeSection will scroll to that section

  const [scrollHeaderSection, setscrollHeaderSection] = useState<{
    tag: string;
    key: string;
    smooth: boolean;
  }>();

  const scrollToSection = useCallback(
    (tag: string, key: string, smooth = true) => {
      setscrollHeaderSection({ tag, key, smooth });
    },
    [setscrollHeaderSection],
  );

  const { isDesktopWidth } = useDeviceWidth();

  return (
    <View
      style={{
        flex: 1,
        height: "100%",
      }}
    >
      {!richTextState?.message && richTextState?.loadedJson && (
        <View
          style={{
            opacity: richTextState.loading ? 0 : 1.0,
            height: "100%",
            width: "100%",
          }}
        >
          <SeaStack
            /** BEWARE: Works in line with the styling in the `SeaTableOfContents` component */
            direction={isDesktopWidth ? "row" : "column"}
            align={"start"}
            style={{
              height: "100%",
            }}
            justify={"between"}
          >
            {/** Table of Contents */}

            <SeaStack
              align={"start"}
              width={isDesktopWidth ? "30%" : "100%"}
              justify={isDesktopWidth ? "start" : "center"}
              style={{
                height: isDesktopWidth ? "100%" : "auto",
              }}
            >
              {sections && (
                <SeaTableOfContents
                  sections={sections}
                  onPress={(sectionItem) => {
                    scrollOnChange.current = false;
                    // Intentionally commented out. Will need it later for edit
                    // setActiveSection(sectionItem.key);
                    scrollToSection(sectionItem.tag, sectionItem.key, true);
                  }}
                />
              )}
            </SeaStack>

            {/** Rich Text Document */}

            <SeaStack
              align={"start"}
              justify={isDesktopWidth ? "start" : "center"}
              style={{
                height: "100%",
                width: isDesktopWidth ? "70%" : "100%",
                flex: 1,
              }}
            >
              <SeaRichTextViewer
                richTextState={richTextState}
                // setSections={(items) => {
                //   console.log("TT-Set Sections", items.length);
                //   setSections(items);
                // }}
                setSections={setSections}
                scrollHeaderSection={
                  scrollHeaderSection ?? { tag: "", key: "", smooth: false }
                }
                showToast={showToast}
                {...rest}
              />
            </SeaStack>
          </SeaStack>
        </View>
      )}
    </View>
  );
};

export default SeaRichText;
