import { SeaDrawerFullWidth } from "@src/components/_atoms/SeaDrawer/SeaDrawerFullWidth";
import { Modal, ScrollView, StyleSheet, View } from "react-native";
import SeaRichText from "@src/components/_dom/lexical/SeaRichText/SeaRichText";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { preventMultiTap, regions } from "@src/lib/util";
import {
  SaveStateAction,
  setSavingStateManager,
} from "@src/managers/SavingStateManager/SavingStateManager";
import { fromByteArray } from "base64-js";
import { makeSfdocSeaFile } from "@src/lib/fileImports";
import {
  CompanyPlanDto,
  CreateCompanyPlanUseCase,
} from "@src/domain/use-cases/companyDocumentRegister/CreateCompanyPlanUseCase";
import { UpdateCompanyPlanUseCase } from "@src/domain/use-cases/companyDocumentRegister/UpdateCompanyPlanUseCase";
import { useServiceContainer } from "@src/providers/ServiceProvider";
import { sharedState } from "@src/shared-state/shared-state";
import { getDefaultCompanyPlan } from "@src/components/_dom/default-to-move/defaultPlan";
import { loadSfdocNew, RichTextState } from "@src/lib/richText";
import { useDeviceWidth } from "@src/hooks/useDevice";

interface EditCompanyPlanProps {
  visible: boolean;
  onClose: () => void;
}

export const EditCompanyPlan = ({ visible, onClose }: EditCompanyPlanProps) => {
  const licenseeId = sharedState.licenseeId.use();
  const userId = sharedState.userId.use();
  const user = sharedState.user.use();
  const companyPlan = sharedState.companyPlan.use();
  const licenseeSettings = sharedState.licenseeSettings.use();
  const companyPlanTitle = licenseeSettings
    ? regions[licenseeSettings?.region]?.companyPlan
    : "";
  const getDefaultContent = useCallback(() => {
    return getDefaultCompanyPlan(companyPlanTitle);
  }, [companyPlanTitle]);

  // Create a ref for the ScrollView
  const scrollViewRef = useRef<ScrollView>(null);

  // Hooks
  const [richTextState, setRichTextState] = useState<RichTextState>({});
  const services = useServiceContainer();
  const { isMobileWidth } = useDeviceWidth();

  // Handle loading
  useEffect(() => {
    if (companyPlan?.sfdoc) {
      const loadDocument = async () => {
        const loadedRichTextState = (await loadSfdocNew(
          companyPlan?.sfdoc ?? undefined,
          getDefaultContent,
          true,
        )) as RichTextState;
        setRichTextState(loadedRichTextState);
      };

      loadDocument();
    }
  }, [companyPlan?.sfdoc]);

  const onSaveChanges = useCallback(
    (json: any) => {
      if (preventMultiTap("companyPlan")) {
        // TODO: Thrown an error and show an error message
        return Promise.resolve(false);
      }
      if (!userId || !licenseeId) {
        // TODO: Thrown an error and show an error message
        return Promise.resolve(false);
      }

      // Set the saving state to display Loading state
      setSavingStateManager({
        action: SaveStateAction.SAVING,
      });

      const encoder = new TextEncoder();
      const utf8Bytes = encoder.encode(JSON.stringify(json));
      const base64 = fromByteArray(utf8Bytes);
      const sfdocFile = makeSfdocSeaFile(
        base64,
        "companyPlans",
        companyPlanTitle,
        userId,
      );

      const dto: CompanyPlanDto = {
        sfdoc: sfdocFile,
      };

      if (companyPlan) {
        const updateCompanyPlan = services.get(UpdateCompanyPlanUseCase);
        updateCompanyPlan
          .execute(dto, userId, licenseeId)
          .then(() => {
            onClose();
            setSavingStateManager({
              action: SaveStateAction.SAVED,
              onCloseTimeout: 2000,
            });
          })
          .catch((err) => {
            onClose();
            setSavingStateManager({
              action: SaveStateAction.ERROR,
            });
            const errorMessage = "ERROR: " + err.message;
            console.error("Failed", errorMessage);
          });
      } else {
        const createCompanyPlan = services.get(CreateCompanyPlanUseCase);
        createCompanyPlan
          .execute(dto, userId, licenseeId)
          .then(() => {
            onClose();
            setSavingStateManager({
              action: SaveStateAction.SAVED,
              onCloseTimeout: 2000,
            });
          })
          .catch((err) => {
            onClose();
            setSavingStateManager({
              action: SaveStateAction.ERROR,
            });
            const errorMessage = "ERROR: " + err.message;
            console.error("Failed", errorMessage);
          });
      }
    },
    [companyPlan, user, userId, licenseeId, services],
  );

  return (
    <View style={styles.mainContainer}>
      <SeaDrawerFullWidth
        title={"Edit Company Plan"}
        visible={visible}
        onClose={() => onClose()}
        contentStyle={
          isMobileWidth
            ? { paddingLeft: 0, paddingRight: 0, paddingVertical: 20 }
            : undefined
        }
      >
        <View style={styles.richTextContainer}>
          <SeaRichText
            forModal={false}
            visible={true}
            setOnScroll={() => console.log("TT-RT-SetOnScroll")}
            modalContentRef={scrollViewRef}
            richTextState={richTextState}
            onlineStatus={true}
            isEditable={true}
            onSaveChanges={onSaveChanges}
          />
        </View>
      </SeaDrawerFullWidth>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    height: "100%",
  },
  richTextContainer: {
    flex: 1,
    height: "100%",
    width: "100%",
  },
});
